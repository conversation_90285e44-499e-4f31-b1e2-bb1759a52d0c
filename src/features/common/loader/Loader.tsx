'use client';
import Image from 'next/image';
import styles from './Loader.module.scss';
import { imageBaseUrl } from '@/constants/envVariables';
import { createPortal } from 'react-dom';

/**
 * @method Loader
 * @description To show a loader wrapper on poastback and other API calls
 * @returns JSX Element
 */
const Loader = (): JSX.Element => {
  return (
    <div className={styles.loader}>
      <div className={styles.loader__image}>
        <Image
          src={`${imageBaseUrl}/images/loader.gif`}
          width={80}
          height={80}
          unoptimized={true}
          priority
          data-testid="loaderImage"
          alt=""
        />
      </div>
    </div>
  );
};

export default Loader;
