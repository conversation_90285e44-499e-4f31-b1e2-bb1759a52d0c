import React, { useState } from 'react';
import styles from './HomeFooter.module.scss';
import Link from 'next/link';
import FooterPaymentPartners from './FooterPaymentPartners';
import { useTranslation } from 'react-i18next';
import { imageBaseUrl, ecomAppUrl } from '@/constants/envVariables';
import { FOOTER_ALIGNMENT, FOOTER_ITEM_TYPE, MENU_TYPE, PAGEURLS } from '@/constants/common';
import HiringTile from './HiringTile';
import sanitizeHTML from '@/utils/sanitizeHTML';
import { nl2brParagraph } from '@/utils/nl2br';

/**
 * @method HomeFooter
 * @description Landing page footer component
 * @returns {JSX.Element}
 */

const HomeFooter = ({ footerData }: any): JSX.Element => {
  
  // Translation
  const { t } = useTranslation('common');
  
  const copyrightNotice = footerData?.copyrightNotice;
  const footerStores = footerData?.footerStores;
  const logo = footerData?.logo;
  const logoWebp = footerData?.logoWebp;
  const subMenu = footerData?.subMenu;
  console.log('subMenu ', subMenu);
  const tagLine = footerData?.tagLine;

  const SocialData = subMenu
    ?.filter((item:any) => item?.itemCode === FOOTER_ITEM_TYPE.ITEM_CODE_OUR_SOCIAL)
    .flatMap((item:any) => item?.children?.edges);

  const supportData = subMenu
    ?.filter((item:any) => item?.itemCode === FOOTER_ITEM_TYPE.ITEM_CODE_SUPPORT)
    .flatMap((item: any) => item?.children?.edges[0]?.node);

  const forGiftcardSolutionData: any = subMenu?.flatMap((section:any) =>
    section?.children?.edges
      ?.map((edge:any) => edge?.node)
      .filter((node:any) => node?.itemCode === FOOTER_ITEM_TYPE.FB_ITEM_CODE)
  );
  const forDevelopersData: any = subMenu?.flatMap((section:any) =>
    section?.children?.edges
      ?.map((edge:any) => edge?.node)
      .filter(
        (node:any) => node?.itemCode === FOOTER_ITEM_TYPE.ITEM_CODE_FOR_DEVELOPERS
      )
  );
  const serviceData = subMenu
  ?.filter(
    (item:any) => item?.itemCode === FOOTER_ITEM_TYPE.ITEM_CODE_OUR_SERVICES
  )
  .flatMap((item:any) => item?.children?.edges);

  const [webpSupported, setWebpSupported] = useState<boolean>(false);

  const footerRibbon = `${imageBaseUrl}/images/icons/footer-ribbon.svg`;
  const apple = `${imageBaseUrl}/images/icons/apple-button.svg`;
  const exportIcon = `${imageBaseUrl}/images/icons/export.svg`;
  const exportWhiteIcon = `${imageBaseUrl}/images/icons/export.svg`;
  const codeBackgroud = `${imageBaseUrl}/images/code-bg.gif`;

  /**
   * @method getStoreLinkHref
   * @param storeLink
   * @returns string
   */
  const getStoreLinkHref = (storeLink: any) => {
    const storeCode = storeLink?.split('/')[2];
    return `/${storeCode}`;
  };
  return (
    <>
      {!!footerData && (
        <footer
          role="footer"
          className={styles.footer}
          data-testid="footer"
          id="e2eTestingFooterWrapper"
        >
          {/* <img
            src={footerRibbon}
            alt="footer-ribbon"
            className={`${styles['footer__robbon']}`}
          /> */}
          <div className={`container ${styles['footer__container']}`}>
            {!!logo ? (
              <img
                src={webpSupported && logoWebp ? logo : logo}
                alt={tagLine}
                className={`${styles['footer-logo']} md-hide`}
                id="e2eTestingFooterLogo"
                data-testid="footerLogo"
              />
            ) : (
              ''
            )}
            <div
              className={`${styles['footer__row']} ${styles['footer__row--first']} ${styles['first-row']}`}
            >
              <div
                className={`${styles['first-row__column']} ${styles['first-row__column--company-data']}`}
              >
                <div>
                  <h5>{t('spreadingHappiness')}</h5>

                  {!!tagLine ? (
                    <p className="md-hide" data-testid="tagline"   dangerouslySetInnerHTML={{
                      __html: nl2brParagraph(sanitizeHTML(tagLine)),
                    }}>
                    </p>
                  ) : (
                    ''
                  )}
                  <div
                    className={`${styles['first-row__column--company-data--social']}`}
                  >
                    {SocialData?.map(
                      (
                        {
                          node: {
                            itemImage = '',
                            itemLabel = '',
                            itemUrl = '',
                            itemImageWebp = '',
                          } = {},
                        }: any,
                        index: number
                      ) => (
                        // <React.Fragment key={index}>
                        //   <Link target='_blank' href={itemUrl}>
                        //     <p>
                        //       <img
                        //         src={
                        //           webpSupported && itemImageWebp
                        //             ? itemImageWebp || itemImage
                        //             : itemImage
                        //         }
                        //         alt={itemLabel}
                        //       />
                        //     </p>
                        //   </Link>
                        // </React.Fragment>
                      )
                    )}
                  </div>
                  {/* <FooterPaymentPartners /> */}
                </div>
                {/* <div>&nbsp;</div> */}
              </div>
              {subMenu?.map(
                (
                  {
                    itemLabel = '',
                    children = { edges: [] },
                    itemAlignment = '',
                    itemCode = '',
                  },
                  index: number
                ) => (
                  <>
                    {itemAlignment === FOOTER_ALIGNMENT.VERTICAL &&
                      itemCode !== FOOTER_ITEM_TYPE.ITEM_CODE_SUPPORT && (
                    <div
                      className={`${styles['first-row__column']} ${styles['first-row__column--nav01']}`}
                      key={index}
                      data-testid={`subFirstRowMenu`}
                    >
                      <h5 data-testid={`subFirstRowMenuLabel`}>{itemLabel}</h5>
                      <div className={styles['footer-nav']}>
                        {children?.edges?.map(
                          (
                            {
                              node: {
                                itemLabel = '',
                                itemUrl = '',
                                itemImage = '',
                                itemCode = ''
                              } = {},
                            },
                            index: number
                          ) => (
                            <div key={index}>
                              {itemUrl &&
                                (itemImage === '' ? (
                                  ""
                                  // <Link
                                  //   href={`${
                                  //     itemUrl?.indexOf('http') !== -1
                                  //       ? itemUrl
                                  //       : `${ecomAppUrl}${itemUrl}`
                                  //   }`}
                                  //   target="__blank"
                                  // >
                                  //   <p
                                  //     id={`e2eTestingFooterLink${itemLabel}`}
                                  //     data-testid="subMenuItem"
                                  //     className={itemCode == MENU_TYPE.CAREER ? styles['hiring-align'] : ""}
                                  //   >
                                  //     {itemLabel} {itemCode == MENU_TYPE.CAREER ? <HiringTile /> : ''}
                                  //   </p>
                                  // </Link>
                                ) : (
                                  ''
                                ))}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                    )}  
                  </>
                )
              )}
              <div
                className={`${styles['first-row__column']} ${styles['first-row__column--store']}`}
              >
                <h5>{t('support')}</h5>
                {/* <p>{t('haveQuestion')}</p> */}
                {/* <div
                  className={`${styles['first-row__column--store__help-center']}`}
                >
                  <p>{t('checkOutOur')} </p>
                  <Link href={supportData[0]?.itemUrl || ''}>
                    <p>{t('helpCentre')}</p>
                  </Link>
                </div> */}
                <Link href={supportData[0]?.itemUrl || ""} legacyBehavior>
                    <a><p>{t("helpCentre")}</p></a>
                </Link>
                <div
                  className={`${styles['first-row__column--store__app-download']}`}
                >
                  <img
                    src={
                      webpSupported && supportData[0]?.itemImageWebp
                        ? supportData[0]?.itemImageWebp
                        : supportData[0]?.itemImage
                    }
                    alt="qr code"
                  />
                  <div
                    className={` ${styles['first-row__column']} ${styles['first-row__column__app-download--text']}`}
                  >
                    <p>{t('downloadapp')}</p>
                  </div>
                </div>
              </div>
            </div>
            {/* <div className={`${styles["footer__row"]} ${styles["footer__tech-GiftCard-Wrapper"]}`}>
              <CustomLink hrefLink={forGiftcardSolutionData[0]?.itemUrl || ''} aTarget="_blank">
                <div className={`${styles["footer__solution-text"]}`}>
                  <div className={`${styles["footer__label-container"]}`}>
                    <span className={`${styles["footer__label-container--caption"]}`}>{forGiftcardSolutionData[0]?.itemCaption}</span>{" "}
                    <span className={`${styles["footer__label-container--label"]}`}>{forGiftcardSolutionData[0]?.itemLabel}</span>{" "}
                    <img src={exportWhiteIcon} alt="" />
                  </div>

                </div>
              </CustomLink>
              <CustomLink hrefLink={forDevelopersData[0]?.itemUrl || ""} aTarget="_blank">
                <div className={`${styles["footer__dev-text"]}`}>
                  <div className={`${styles["footer__label-container"]}`}>
                    <span className={`${styles["footer__label-container--caption"]}`}>{forDevelopersData[0]?.itemCaption}</span>{" "}
                    <span className={`${styles["footer__label-container--label"]}`}>{forDevelopersData[0]?.itemLabel}</span>{" "}
                    <img src={exportWhiteIcon} alt="" />
                  </div>

                </div>
              </CustomLink>
            </div> */}
            <div
              className={`${styles['footer__row']} ${styles['footer__row--second']} ${styles['second-row']}`}
              id="e2eTestingFooterCopyrightWrapper"
            >
              {/* <div
                className={styles['second-row__copyright']}
                data-testid="copyright"
              >
                {copyrightNotice}
              </div> */}
              <div className={styles['second-row__copyright-container']}>
                <div
                  className={styles["second-row__copyright"]}
                  data-testid="copyright"
                >
                  {copyrightNotice}
                </div>
                <Link href={PAGEURLS.PRIVACY_POLICY} legacyBehavior>
                  <a target="_blank">
                    {t('privacyPolicy')}
                  </a>
                </Link>
                <Link href={PAGEURLS.TERMS} legacyBehavior>
                  <a target="_blank">
                    {t('termsOfUseOBold')}
                  </a>
                </Link>
                <FooterPaymentPartners />
              </div>
              <div className={styles['second-row__available-regions']}>
                {!!footerStores?.length ? t('availableIn') : ''}&nbsp;
                {footerStores?.map((item:any, index: number) => {
                  return (
                    <React.Fragment key={index}>
                      <Link
                        href={getStoreLinkHref(item?.storeLink)}
                        locale={false}
                        legacyBehavior
                      >
                        <p>
                          <span key={index} data-testid="availableStores">
                            {item?.store?.country?.name}
                            {footerStores?.length !== index + 1 && (
                              <>
                                <span className={styles.sep}>|</span>{' '}
                              </>
                            )}
                          </span>
                        </p>
                      </Link>
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          </div>
        </footer>
      )}
    </>
  );
};

export default HomeFooter;
